package glamour

import (
	"io"
	"strings"
)

// ChunkedLineWriter writes terminal output in chunks based on visible line count.
type ChunkedLineWriter struct {
	linesPerChunk int
	lineBuffer    []string
	output        io.Writer
	partialLine   string
}

// NewChunkedLineWriter constructs a writer with chunking by lines.
func NewChunkedLineWriter(w io.Writer, linesPerChunk int) *ChunkedLineWriter {
	if linesPerChunk <= 0 {
		panic("linesPerChunk must be greater than 0")
	}
	return &ChunkedLineWriter{
		linesPerChunk: linesPerChunk,
		output:        w,
	}
}

// Write implements io.Writer and processes input into visible line chunks.
func (clw *ChunkedLineWriter) Write(p []byte) (n int, err error) {
	input := clw.partialLine + string(p)
	lines := splitPreserveNewline(input)

	clw.partialLine = ""

	// If the input doesn't end with a newline, buffer the last partial line
	if !strings.HasSuffix(input, "\n") {
		clw.partialLine = lines[len(lines)-1]
		lines = lines[:len(lines)-1]
	}

	for _, line := range lines {
		clw.lineBuffer = append(clw.lineBuffer, line)
		if clw.visibleLineCount() >= clw.linesPerChunk {
			if err := clw.flushChunk(); err != nil {
				return 0, err
			}
		}
	}

	return len(p), nil
}

// Close flushes any remaining buffered content as a chunk.
func (clw *ChunkedLineWriter) Close() error {
	if clw.partialLine != "" {
		clw.lineBuffer = append(clw.lineBuffer, clw.partialLine)
		clw.partialLine = ""
	}
	return clw.flushChunk()
}

// visibleLineCount counts actual displayed lines (not control sequences)
func (clw *ChunkedLineWriter) visibleLineCount() int {
	count := 0
	for _, line := range clw.lineBuffer {
		if strings.Contains(line, "\n") {
			count++
		}
	}
	return count
}

// flushChunk writes a chunk of buffered lines to the output with a separator.
func (clw *ChunkedLineWriter) flushChunk() error {
	if len(clw.lineBuffer) == 0 {
		return nil
	}
	chunk := strings.Join(clw.lineBuffer, "")
	chunk += "---\n"
	_, err := clw.output.Write([]byte(chunk))
	clw.lineBuffer = clw.lineBuffer[:0]
	return err
}

// splitPreserveNewline splits input into lines keeping the newline char.
func splitPreserveNewline(s string) []string {
	var lines []string
	var buf strings.Builder

	for i := 0; i < len(s); i++ {
		ch := s[i]
		buf.WriteByte(ch)

		if ch == '\n' {
			lines = append(lines, buf.String())
			buf.Reset()
		}
	}
	if buf.Len() > 0 {
		lines = append(lines, buf.String())
	}
	return lines
}
